<script lang="ts" setup>
import { ref, nextTick } from "vue"
import { ElMessage } from "element-plus"
import { sendChatMessageApi } from "@@/apis/chat"
import type { ChatMessage } from "@@/apis/chat/type"

defineOptions({
  name: "ChatSidebar"
})

// 聊天相关
const chatMessage = ref('')
const chatMessages = ref<ChatMessage[]>([
  {
    id: 1,
    type: 'assistant',
    content: '您好！我是海豚AI助手，可以帮助您分析超声图像和回答医学相关问题。请告诉我您需要什么帮助？',
    timestamp: new Date()
  }
])
const chatMessagesRef = ref<HTMLElement>()
const isLoading = ref(false)

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatMessagesRef.value) {
      chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight
    }
  })
}

// 发送聊天消息
const sendMessage = async () => {
  if (!chatMessage.value.trim() || isLoading.value) return

  const userMessage = chatMessage.value.trim()
  const userMessageId = Date.now()

  // 添加用户消息
  chatMessages.value.push({
    id: userMessageId,
    type: 'user',
    content: userMessage,
    timestamp: new Date()
  })

  // 清空输入框
  chatMessage.value = ''
  scrollToBottom()

  // 添加加载中的AI消息
  const loadingMessageId = userMessageId + 1
  chatMessages.value.push({
    id: loadingMessageId,
    type: 'assistant',
    content: '正在思考中...',
    timestamp: new Date(),
    loading: true
  })

  isLoading.value = true
  scrollToBottom()

  try {
    // 调用聊天API
    const response = await sendChatMessageApi({ question: userMessage })

    // 移除加载消息
    const loadingIndex = chatMessages.value.findIndex(msg => msg.id === loadingMessageId)
    if (loadingIndex !== -1) {
      chatMessages.value.splice(loadingIndex, 1)
    }

    // 添加AI回复
    chatMessages.value.push({
      id: Date.now(),
      type: 'assistant',
      content: response.data || '抱歉，我暂时无法回答您的问题。',
      timestamp: new Date()
    })

  } catch (error) {
    console.error('聊天请求失败:', error)

    // 移除加载消息
    const loadingIndex = chatMessages.value.findIndex(msg => msg.id === loadingMessageId)
    if (loadingIndex !== -1) {
      chatMessages.value.splice(loadingIndex, 1)
    }

    // 添加错误消息
    chatMessages.value.push({
      id: Date.now(),
      type: 'assistant',
      content: '抱歉，网络连接出现问题，请稍后重试。',
      timestamp: new Date()
    })

    ElMessage.error('发送消息失败，请稍后重试')
  } finally {
    isLoading.value = false
    scrollToBottom()
  }
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<template>
  <!-- 右侧超声聊天区域 -->
  <div class="chat-sidebar">
    <div class="chat-container">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <h3 class="chat-title">海豚AI助手</h3>
      </div>
      
      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="chatMessagesRef">
        <div class="message-list">
          <div
            v-for="message in chatMessages"
            :key="message.id"
            :class="['message-item', message.type === 'user' ? 'user-message' : 'ai-message']"
          >
            <div class="message-content" :class="{ 'loading': message.loading }">
              <span v-if="message.loading" class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </span>
              <span v-else>{{ message.content }}</span>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>
      </div>
      
      <!-- 聊天输入区域 -->
      <div class="chat-input">
        <el-input
          v-model="chatMessage"
          type="textarea"
          :rows="3"
          placeholder="输入您的问题..."
          resize="none"
          :disabled="isLoading"
          @keydown.enter.prevent="sendMessage"
        />
        <div class="input-actions">
          <el-button
            type="primary"
            @click="sendMessage"
            :disabled="!chatMessage.trim() || isLoading"
            :loading="isLoading"
          >
            {{ isLoading ? '发送中...' : '发送' }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.chat-sidebar {
  width: 350px;
  flex-shrink: 0;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .chat-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .chat-header {
      padding: 16px 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      background: var(--el-color-primary);
      border-radius: 8px 8px 0 0;

      .chat-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: white;
      }
    }

    .chat-messages {
      flex: 1;
      padding: 16px;
      overflow-y: auto;

      .message-list {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .message-item {
          display: flex;
          flex-direction: column;

          &.user-message {
            align-items: flex-end;

            .message-content {
              background: var(--el-color-primary);
              color: white;
              max-width: 80%;
              padding: 10px 14px;
              border-radius: 18px 18px 4px 18px;
              font-size: 14px;
              line-height: 1.4;
            }
          }

          &.ai-message {
            align-items: flex-start;

            .message-content {
              background: var(--el-fill-color-light);
              color: var(--el-text-color-primary);
              max-width: 80%;
              padding: 10px 14px;
              border-radius: 18px 18px 18px 4px;
              font-size: 14px;
              line-height: 1.4;
            }
          }

          .message-time {
            font-size: 12px;
            color: var(--el-text-color-placeholder);
            margin-top: 4px;
            padding: 0 14px;
          }

          // 加载状态样式
          .message-content.loading {
            display: flex;
            align-items: center;
            min-height: 20px;
          }

          .loading-dots {
            display: flex;
            gap: 4px;

            span {
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background-color: var(--el-text-color-placeholder);
              animation: loading-bounce 1.4s infinite ease-in-out both;

              &:nth-child(1) { animation-delay: -0.32s; }
              &:nth-child(2) { animation-delay: -0.16s; }
              &:nth-child(3) { animation-delay: 0s; }
            }
          }
        }
      }
    }

    .chat-input {
      padding: 16px;
      border-top: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);

      .input-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
      }
    }
  }
}

// 加载动画关键帧
@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

// 响应式设计 - 在移动端隐藏
@media screen and (max-width: 768px) {
  .chat-sidebar {
    display: none;
  }
}
</style>
