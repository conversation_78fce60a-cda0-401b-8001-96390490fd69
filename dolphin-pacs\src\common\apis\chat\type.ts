/** 聊天请求参数 */
export interface ChatRequestData {
  /** 用户问题 */
  question: string
}

/** 聊天响应数据 */
export interface ChatResponseData {
  /** 响应状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** AI回复内容 */
  data: {
    /** AI回复的文本内容 */
    answer: string
    /** 回复时间戳 */
    timestamp?: string
    /** 会话ID（如果需要） */
    sessionId?: string
  }
}

/** 聊天消息类型 */
export interface ChatMessage {
  /** 消息ID */
  id: string | number
  /** 消息类型 */
  type: 'user' | 'assistant'
  /** 消息内容 */
  content: string
  /** 消息时间 */
  timestamp: Date
  /** 是否正在加载 */
  loading?: boolean
}
